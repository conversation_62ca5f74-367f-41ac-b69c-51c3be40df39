# 常用模式和最佳实践

- 移动端优化最佳实践：1.使用响应式文字hidden sm:inline显示不同内容 2.触控区域最小44px 3.网格布局grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 4.间距space-y-4 sm:space-y-6渐进式增加
- 响应式宽度对齐最佳实践：使用统一的max-w断点类名（max-w-md lg:max-w-lg xl:max-w-xl）确保不同元素在各屏幕尺寸下保持一致的视觉宽度，避免w-full无限制导致的对齐问题
- 2025年现代化UI设计最佳实践：bg-white/80 backdrop-blur-sm毛玻璃效果，shadow-xl阴影，border border-white/20边框，渐变背景from-indigo-50 via-white to-purple-50，emoji图标增强视觉效果，stats组件展示数据，badge组件标签，btn-lg h-16大按钮设计
- Anime.js v4移动端动画最佳实践：createScope创建动画作用域，支持媒体查询检测，prefers-reduced-motion无障碍支持，stagger错开动画，页面加载动画，卡片hover动画，按钮点击反馈动画，60fps性能优化
- 实时日期时间组件最佳实践：useState管理当前时间，setInterval每秒更新，toLocaleDateString中文格式化，font-mono等宽字体，time-digit类名配合Anime.js动画，毛玻璃卡片设计，响应式文字大小
- 炫酷UI设计最佳实践：渐变背景装饰圆形元素，毛玻璃backdrop-blur-sm效果，进度条可视化数据，emoji图标圆形容器，bg-clip-text渐变文字，相对定位装饰元素，shadow-lg阴影层次，border渐变边框，Math.round整数显示
- 进度条数据驱动最佳实践：Math.min((actual/target)*100,100)%动态计算，营养素合理目标值(蛋白质150g脂肪80g碳水300g纤维35g)，代谢数据目标值(BMR2500TDEE3500)，避免固定百分比装饰性进度条
- 营养建议组件现代化重构最佳实践：emoji图标替代Heroicons，渐变背景色彩主题，毛玻璃图标容器，背景装饰元素，hover缩放动画，响应式文字大小，Math.round整数显示，移除复杂依赖简化组件
- 卡片设计统一最佳实践：避免双层包装设计，直接使用渐变背景卡片，移除不必要的白色毛玻璃包装层，保持图标容器的一致性设计，简化组件导入减少依赖，统一视觉层次和设计语言
- 底部导航栏组件最佳实践：fixed bottom-0定位，bg-white/90 backdrop-blur-sm毛玻璃效果，grid grid-cols-3三等分布局，h-16确保44px触控区域，点击缩放动画，活跃状态高亮，iOS安全区域适配，z-50层级管理
- 简洁列表式UI设计最佳实践：使用border-b分割线替代卡片包装，flex布局emoji图标+内容，统一色彩主题避免过度装饰，三餐数据显示格式实际/目标(百分比%)，Math.min限制进度条最大100%
- DaisyUI dock组件最佳实践：使用dock类替代自定义grid布局，dock-active管理活跃状态，dock-label显示按钮标签，fixed定位确保固定显示，backdrop-blur-sm毛玻璃效果，z-50层级管理，iOS安全区域适配
- 原生App底部Tab导航最佳实践：fixed定位到视口底部，env(safe-area-inset-bottom)全面屏适配，backdrop-filter毛玻璃效果，touch-manipulation触控优化，DaisyUI dock组件，dock-active状态管理，拇指触手可及的位置设计
- 底部导航栏Context7最佳实践：position:fixed bottom:0确保真正固定，backdrop-filter:blur(12px)毛玻璃效果，env(safe-area-inset-bottom)安全区域适配，touch-action:manipulation触控优化，z-index:50层级管理，DaisyUI dock组件集成，完全删除重建确保代码质量
- CSS fixed定位问题排查最佳实践：检查父容器transform属性影响，PageTransition组件的transform-gpu会创建层叠上下文，使用!important确保CSS优先级，动画完成后移除transform避免持续影响，z-index使用9999确保最高层级
- Context7底部导航栏最佳实践：避免React Portal和原生DOM操作，使用fixed bottom-0 inset-x-0 z-50定位，Tailwind CSS类替代内联样式，简洁的flex布局，backdrop-blur-sm毛玻璃效果，pb-safe安全区域适配，声明式组件树渲染
- Context7移动端底部导航栏最佳实践：position:fixed+内联样式确保定位，max(env(safe-area-inset-bottom), 8px)安全区域，min-w-[56px] min-h-[44px]触控标准，onTouchStart触控反馈，backdrop-blur-xl毛玻璃，shadow-2xl阴影，touch-manipulation优化，aria-label无障碍
- 数字输入优化最佳实践：-webkit-appearance:none移除步进器，-moz-appearance:textfield兼容Firefox，::-webkit-inner-spin-button隐藏控件，统一min-w-[64px] min-h-[52px]按钮尺寸，hover:scale-105微交互，duration-300流畅动画
- 智能推荐算法最佳实践：基于BMI健康范围18.5-24.9，活动水平系数sedentary:0.8到veryActive:1.6，时间因子weeksAvailable*adjustedWeeklyLoss，合理边界Math.max(healthyMinWeight)和Math.min(currentWeight)，用户友好的至少减重2kg保证
- CSS层级冲突解决最佳实践：使用32位整数最大值**********作为绝对最高z-index，position:fixed!important强制定位，display:block!important和visibility:visible!important确保可见性，全局CSS选择器nav[style*="zIndex"]提供额外保障，限制其他元素z-index避免冲突
- 底部导航栏层叠上下文冲突完整解决方案标准化实施：1.组件结构重构-将BottomNavigation移到根级别脱离Anime.js transform影响；2.绝对层级保障-使用z-index:**********和!important强制优先级；3.全局CSS多重保障-bottom-navigation类选择器和transform容器特殊处理；4.跨页面一致性-Dashboard和CalendarPage都采用相同结构；5.安全区域适配-env(safe-area-inset-bottom)支持全面屏设备
- 已完成Gemini API提示词全面优化：1)营养标签优先策略和OCR增强 2)智能文本解析和上下文推理 3)多图片处理和跨图片一致性 4)JSON响应稳定性提升(降低temperature、优化参数) 5)数据结构扩展(营养标签信息、分量分析、元数据) 6)单图片优化路径和错误恢复机制
