import { FoodRecognitionResult } from '@/shared/types';

/**
 * Google Gemini AI服务
 */
export class GeminiService {
  private apiKey: string;
  private endpoint: string;
  private model: string;
  private timeout: number;


  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY || 'AIzaSyDiUlo8Xn6p6F0ZOtbodegVhPq8p-epigo';
    this.model = import.meta.env.VITE_GEMINI_MODEL || 'gemini-2.5-flash';
    this.timeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');

    // 动态设置API端点
    this.endpoint = this.getApiEndpoint();

    // 添加API端点使用日志
    console.log(`Gemini API端点: ${this.endpoint}`);
    console.log(`Gemini 模型: ${this.model}`);

    if (!this.apiKey) {
      console.warn('Gemini API密钥未配置');
    }
  }

  /**
   * 获取API端点
   */
  private getApiEndpoint(): string {
    // 优先使用环境变量配置的端点
    const envEndpoint = import.meta.env.VITE_GEMINI_API_ENDPOINT;
    if (envEndpoint) {
      console.log('使用环境变量配置的端点:', envEndpoint);
      return envEndpoint;
    }

    // 使用新的代理端点
    console.log('使用代理端点: http://geminiapi.hinetlove.site:5321');
    return 'http://geminiapi.hinetlove.site:5321';
  }



  /**
   * 识别食物图片（支持多张图片）
   */
  async recognizeFood(imageFiles: File | File[], additionalContext?: string, userContext?: any): Promise<FoodRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      // 处理单张或多张图片
      const files = Array.isArray(imageFiles) ? imageFiles : [imageFiles];

      // 验证图片数量限制（根据Context7查询结果，Gemini支持多图片）
      if (files.length > 5) {
        throw new Error('最多支持5张图片同时识别');
      }

      // 将所有图片转换为base64
      const base64Images = await Promise.all(
        files.map(file => this.fileToBase64(file))
      );

      // 根据图片数量选择不同的处理方式
      let response: any;
      if (files.length === 1) {
        // 单图片使用优化的单图片识别
        response = await this.sendSingleImageRequest(base64Images[0], additionalContext);
      } else {
        // 多图片使用多图片识别
        response = await this.sendMultiImageRequest(base64Images, additionalContext, userContext);
      }

      // 解析响应
      return this.parseResponse(response, 'image');
    } catch (error) {
      console.error('食物识别失败:', error);
      throw new Error(error instanceof Error ? error.message : '食物识别失败');
    }
  }

  /**
   * 专门的营养建议分析 - 使用更高的温度参数获得更有创造性的建议
   */
  async analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }> {
    if (!this.apiKey) {
      console.error('Gemini API密钥未配置');
      throw new Error('Gemini API密钥未配置');
    }

    console.log('发送营养建议请求到Gemini API...');
    console.log('API端点：', this.endpoint);
    console.log('提示词预览：', prompt.substring(0, 200) + '...');

    try {
      const response = await fetch(`${this.endpoint}/v1beta/models/${this.model}:generateContent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              role: 'user',
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.6,  // 平衡创造性和准确性的温度参数
            topK: 40,          // 增加候选词数量以提高建议多样性
            topP: 0.9,         // 保持较高的随机性以获得创造性建议
            maxOutputTokens: 2048,  // 适当的输出长度
            candidateCount: 1  // 只生成一个候选结果
          }
        }),
      });

      console.log('API响应状态：', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API请求失败：', response.status, errorText);
        throw new Error(`Gemini API请求失败: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('API响应数据：', JSON.stringify(data, null, 2));

      // 提取AI建议文本
      if (data.candidates && data.candidates.length > 0) {
        const content = data.candidates[0].content;
        console.log('候选内容：', content);

        if (content && content.parts && content.parts.length > 0) {
          const advice = content.parts[0].text;
          console.log('提取的建议文本：', advice);

          return {
            advice: advice?.trim() || '暂无建议',
            rawResponse: data
          };
        } else {
          console.log('内容结构异常：', content);
        }
      } else {
        console.log('无候选响应：', data);
      }

      return {
        advice: '暂无建议',
        rawResponse: data
      };

    } catch (error) {
      console.error('营养建议分析失败:', error);
      console.error('错误详情:', {
        message: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * 分析文字中的食物信息
   */
  async analyzeTextFood(text: string): Promise<FoodRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      // 发送文字分析请求
      const response = await this.sendTextRequest(text);

      // 解析响应
      return this.parseResponse(response, 'text');
    } catch (error) {
      console.error('文字分析失败:', error);
      throw new Error(error instanceof Error ? error.message : '文字分析失败');
    }
  }

  /**
   * 构建增强版食物识别提示词 - 全面优化版本
   */
  private buildFoodRecognitionPrompt(additionalContext?: string): string {
    return `
**系统角色：** 你是一位专业的营养师和食物识别专家，具备精确的营养成分分析能力和OCR文字识别技能。

**核心任务：** 分析图片中的食物并提供准确的营养信息。

**重要：必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字**

**分析优先级（按重要性排序）：**

1. **营养标签优先原则**
   - 首先仔细扫描图片中的营养成分表、营养标签、包装信息
   - 如发现营养标签，请精确读取以下信息：
     * 每份重量/体积
     * 每份卡路里
     * 蛋白质、脂肪、碳水化合物含量
     * 纤维、糖分、钠含量
     * 标注数据来源为 "nutrition_label"
     * 置信度设为 0.9-1.0（基于标签清晰度）

2. **视觉食物识别**
   - 识别所有可见的食物项目
   - 估算每种食物的重量（克）
   - 基于标准营养数据库计算营养成分
   - 标注数据来源为 "visual_estimation"
   - 置信度设为 0.6-0.8（基于识别清晰度）

3. **用户上下文整合**
${additionalContext ? `   - 用户提供的额外信息：${additionalContext}
   - 将此信息作为重要参考，用于修正识别结果
   - 如用户描述与视觉不符，优先考虑用户描述` : '   - 无额外用户信息'}

**详细分析要求：**

• **食物识别精度**：准确识别每种食物的名称、品牌（如有）
• **重量估算**：基于视觉线索（餐具、包装、手部比例）估算重量
• **营养计算**：使用标准营养数据库进行精确计算
• **置信度评估**：真实反映识别的准确性和可靠性
• **数据来源标注**：明确区分营养标签数据和视觉估算数据

**严格JSON响应格式：**
{
  "foods": [
    {
      "name": "精确的食物名称（包含品牌信息如有）",
      "calories": 数值（整数），
      "weight": 重量克数（整数），
      "confidence": 置信度（0.0-1.0，保留2位小数），
      "alternatives": ["可能的替代识别结果数组"],
      "dataSource": "nutrition_label" | "visual_estimation" | "hybrid",
      "nutrition": {
        "protein": 蛋白质克数（保留1位小数），
        "fat": 脂肪克数（保留1位小数），
        "carbs": 碳水化合物克数（保留1位小数），
        "fiber": 纤维克数（保留1位小数），
        "sugar": 糖分克数（保留1位小数），
        "sodium": 钠毫克数（整数）
      },
      "labelInfo": {
        "hasLabel": 布尔值,
        "servingSize": "份量描述字符串或null",
        "labelAccuracy": 标签读取准确度（0.0-1.0）或null,
        "brandName": "品牌名称或null",
        "productName": "产品名称或null"
      },
      "portionAnalysis": {
        "estimatedPortion": 估算食用分量（0.0-1.0），
        "totalPackageWeight": 整包重量克数或null,
        "consumedWeight": 实际食用重量克数
      }
    }
  ],
  "analysisMetadata": {
    "hasNutritionLabel": 布尔值,
    "imageQuality": "high" | "medium" | "low",
    "recognitionMethod": "label_priority" | "visual_only" | "hybrid",
    "processingNotes": "分析过程中的重要注意事项"
  }
}

**关键处理规则：**
• **空图片检测**：如图片无食物内容，返回 {"foods": [], "analysisMetadata": {"hasNutritionLabel": false, "imageQuality": "unknown", "recognitionMethod": "none", "processingNotes": "未检测到食物内容"}}
• **营养标签优先**：发现标签时，优先使用标签数据，置信度0.9+
• **视觉估算补充**：无标签时使用视觉估算，置信度0.6-0.8
• **混合模式**：标签+视觉结合时，dataSource设为"hybrid"
• **分量计算**：区分包装总重量和实际食用重量
• **品质控制**：确保所有数值合理，营养成分总和符合卡路里计算

**输出要求：直接返回JSON，无任何额外文字、标记或解释**
    `.trim();
  }

  /**
   * 构建增强版文字分析提示词 - 全面优化版本
   */
  private buildTextAnalysisPrompt(text: string): string {
    return `
**系统角色：** 你是一位专业的营养师和食物分析专家，具备从文字描述中精确提取食物信息的能力。

**核心任务：** 从用户的文字描述中识别食物并提供准确的营养分析。

**重要：必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字**

**用户输入文本：** "${text}"

**分析策略：**

1. **智能文本解析**
   - 识别所有明确提到的食物名称
   - 提取数量、重量、体积等量化信息
   - 识别烹饪方式、调料、配菜等影响营养的因素
   - 理解口语化表达（如"一碗"、"一份"、"半个"等）

2. **上下文推理**
   - 根据描述推断食物的大致分量
   - 考虑常见的食物搭配和用餐习惯
   - 基于烹饪方式调整营养成分（如油炸vs蒸煮）
   - 识别可能的隐含食物（如汉堡包含面包、肉饼、蔬菜等）

3. **营养数据估算**
   - 使用标准营养数据库进行计算
   - 根据烹饪方式调整营养成分
   - 考虑食物组合的营养交互作用
   - 提供合理的营养成分分布

**严格JSON响应格式：**
{
  "foods": [
    {
      "name": "精确的食物名称",
      "calories": 卡路里数值（整数），
      "weight": 重量克数（整数），
      "confidence": 置信度（0.0-1.0，保留2位小数），
      "dataSource": "text_analysis",
      "alternatives": ["可能的替代解释数组"],
      "nutrition": {
        "protein": 蛋白质克数（保留1位小数），
        "fat": 脂肪克数（保留1位小数），
        "carbs": 碳水化合物克数（保留1位小数），
        "fiber": 纤维克数（保留1位小数），
        "sugar": 糖分克数（保留1位小数），
        "sodium": 钠毫克数（整数）
      },
      "textAnalysis": {
        "originalPhrase": "原始文字描述片段",
        "interpretedQuantity": "解释的数量信息",
        "cookingMethod": "烹饪方式或null",
        "portionReference": "分量参考（如一碗、一份等）"
      }
    }
  ],
  "analysisMetadata": {
    "textQuality": "high" | "medium" | "low",
    "ambiguityLevel": "low" | "medium" | "high",
    "extractedFoodCount": 识别到的食物数量,
    "processingNotes": "文本分析过程中的重要注意事项"
  }
}

**关键处理规则：**
• **空文本检测**：如文字无食物信息，返回 {"foods": [], "analysisMetadata": {"textQuality": "low", "ambiguityLevel": "high", "extractedFoodCount": 0, "processingNotes": "未识别到明确的食物信息"}}
• **量化推理**：将模糊描述转换为具体重量（如"一碗米饭"→150g）
• **营养调整**：根据烹饪方式调整营养成分（如炒菜增加油脂）
• **置信度评估**：基于描述的明确程度和常见程度设定置信度
• **上下文理解**：考虑食物搭配和用餐场景的合理性

**输出要求：直接返回JSON，无任何额外文字、标记或解释**

    `.trim();
  }

  /**
   * 发送单图片识别API请求 - 使用优化的单图片提示词
   */
  private async sendSingleImageRequest(base64Image: string, additionalContext?: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 构建单图片内容
      const imagePart = {
        inline_data: {
          mime_type: 'image/jpeg',
          data: base64Image
        }
      };

      // 使用优化的单图片提示词
      const prompt = this.buildFoodRecognitionPrompt(additionalContext);

      // 构建请求体
      const requestBody = {
        contents: [{
          role: 'user',
          parts: [
            { text: prompt },
            imagePart
          ]
        }],
        generationConfig: {
          temperature: 0.05,  // 降低温度以提高JSON格式稳定性
          topK: 16,           // 减少候选词数量，提高一致性
          topP: 0.8,          // 降低随机性，确保结构化输出
          maxOutputTokens: 4096,  // 适当的输出长度
          candidateCount: 1,  // 只生成一个候选结果
          stopSequences: ["```", "```json", "```JSON"]  // 防止markdown格式
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      // 构建API URL
      const apiUrl = `${this.endpoint}/v1beta/models/${this.model}:generateContent`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }

      throw error;
    }
  }

  /**
   * 发送多图片识别API请求（基于Context7最佳实践）
   */
  private async sendMultiImageRequest(base64Images: string[], additionalContext?: string, userContext?: any): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 构建多图片内容数组
      const imageParts = base64Images.map((base64Image) => ({
        inline_data: {
          mime_type: 'image/jpeg', // 根据Context7文档，需要明确指定MIME类型
          data: base64Image
        }
      }));

      // 构建用户上下文信息（用于个性化建议）
      const userContextPrompt = userContext ? `
用户个人信息：
- 年龄：${userContext.age}岁
- 性别：${userContext.gender}
- 体重：${userContext.weight.toFixed(2)}kg
- 身高：${userContext.height}cm
- 目标体重：${userContext.targetWeight.toFixed(2)}kg
- 活动水平：${userContext.activityLevel}
- 基础代谢率：${Math.round(userContext.bmr)} kcal/天
- 总消耗：${Math.round(userContext.tdee)} kcal/天
- 目标天数：${userContext.targetDays}天

请根据以上个人信息提供个性化的营养建议和运动建议。
` : '';

      // 构建增强版多图片分析提示词
      const prompt = `**系统角色：** 你是一位顶级的营养师和多模态食物识别专家，具备同时分析多张图片并提供综合营养建议的能力。

**核心任务：** 分析这${base64Images.length}张图片中的所有食物，提供准确的营养信息和个性化建议。

**重要：必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字**

${userContextPrompt}

${additionalContext ? `**用户补充信息：** ${additionalContext}` : ''}

**多图片分析策略：**

1. **跨图片食物识别**
   - 逐张分析每张图片中的食物
   - 识别重复出现的食物（避免重复计算）
   - 检测食物的不同角度或状态
   - 合并相同食物的营养信息

2. **营养标签优先处理**
   - 优先识别任何图片中的营养标签
   - 将标签信息与对应的食物匹配
   - 使用标签数据校正视觉估算结果

3. **综合营养分析**
   - 计算所有食物的总营养成分
   - 分析营养搭配的合理性
   - 识别营养缺口或过量风险

**严格JSON响应格式：**
{
  "foods": [
    {
      "name": "精确的食物名称（包含品牌信息如有）",
      "calories": 卡路里数值（整数），
      "weight": 重量克数（整数），
      "confidence": 置信度（0.0-1.0，保留2位小数），
      "dataSource": "nutrition_label" | "visual_estimation" | "hybrid",
      "imageIndex": 图片索引（0开始），
      "alternatives": ["可能的替代识别结果"],
      "nutrition": {
        "protein": 蛋白质克数（保留1位小数），
        "fat": 脂肪克数（保留1位小数），
        "carbs": 碳水化合物克数（保留1位小数），
        "fiber": 纤维克数（保留1位小数），
        "sugar": 糖分克数（保留1位小数），
        "sodium": 钠毫克数（整数）
      },
      "labelInfo": {
        "hasLabel": 布尔值,
        "servingSize": "份量描述或null",
        "labelAccuracy": 标签读取准确度或null,
        "brandName": "品牌名称或null"
      }
    }
  ],
  "personalizedAdvice": "基于用户个人数据的详细个性化营养建议",
  "exerciseAdvice": "针对用户的专属运动消耗建议",
  "multiImageAnalysis": {
    "totalImages": 图片总数,
    "foodItemsFound": 识别到的食物项目总数,
    "duplicatesDetected": 检测到的重复食物数量,
    "overallNutritionSummary": {
      "totalCalories": 总卡路里,
      "totalProtein": 总蛋白质,
      "totalFat": 总脂肪,
      "totalCarbs": 总碳水化合物
    }
  }
}

**关键处理要求：**
• **跨图片一致性**：确保相同食物在不同图片中的识别结果一致
• **营养标签优先**：任何图片中的营养标签都应优先使用
• **重复检测**：避免将同一食物在不同图片中重复计算
• **个性化建议**：基于用户信息提供具体的营养和运动建议
• **质量控制**：所有营养数据必须合理且相互一致
• **图片索引**：为每个食物标注来源图片的索引号

**输出要求：直接返回JSON，无任何额外文字、标记或解释**`;

      // 构建请求体（基于Context7文档的最佳实践）
      const requestBody = {
        contents: [{
          role: 'user',
          parts: [
            { text: prompt },
            ...imageParts
          ]
        }],
        generationConfig: {
          temperature: 0.05,  // 降低温度以提高JSON格式稳定性
          topK: 16,           // 减少候选词数量，提高一致性
          topP: 0.8,          // 降低随机性，确保结构化输出
          maxOutputTokens: 6144,  // 增加输出长度以支持详细分析
          candidateCount: 1,  // 只生成一个候选结果
          stopSequences: ["```", "```json", "```JSON"]  // 防止markdown格式
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      // 构建API URL
      const apiUrl = `${this.endpoint}/v1beta/models/${this.model}:generateContent`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer'+this.apiKey,
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }

      throw error;
    }
  }



  /**
   * 发送文字分析API请求
   */
  private async sendTextRequest(text: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 使用正确的Gemini API端点和认证方式
      const response = await fetch(`${this.endpoint}/v1beta/models/${this.model}:generateContent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer '+this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              role: 'user',
              parts: [
                {
                  text: this.buildTextAnalysisPrompt(text)
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.05,  // 降低温度以提高JSON格式稳定性
            topK: 8,            // 适度增加候选词，平衡准确性和多样性
            topP: 0.7,          // 降低随机性，确保结构化输出
            maxOutputTokens: 3072,  // 增加输出长度以支持详细分析
            candidateCount: 1,  // 只生成一个候选结果
            stopSequences: ["```", "```json", "```JSON"]  // 防止markdown格式
          }
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }

      throw error;
    }
  }

  /**
   * 解析API响应
   */
  private parseResponse(response: any, method: 'image' | 'text' = 'image'): FoodRecognitionResult {
    try {
      // 提取生成的文本
      const candidates = response.candidates;
      if (!candidates || candidates.length === 0) {
        throw new Error('API响应格式错误：没有候选结果');
      }

      const content = candidates[0].content;
      if (!content || !content.parts || content.parts.length === 0) {
        throw new Error('API响应格式错误：没有内容');
      }

      const text = content.parts[0].text;
      if (!text) {
        throw new Error('API响应格式错误：没有文本内容');
      }

      // 增强版JSON解析 - 支持更复杂的清理和验证
      let jsonData: any;
      try {
        // 多层次文本清理
        let cleanText = text;

        // 移除各种可能的markdown标记
        cleanText = cleanText.replace(/```json\s*/gi, '').replace(/```\s*/g, '');
        cleanText = cleanText.replace(/^[^{]*/, '').replace(/[^}]*$/, '');

        // 移除可能的前后缀文字
        cleanText = cleanText.replace(/^.*?(?=\{)/s, '').replace(/\}.*$/s, '}');

        // 尝试提取最完整的JSON对象
        const jsonMatches = cleanText.match(/\{[\s\S]*\}/g);
        if (jsonMatches && jsonMatches.length > 0) {
          // 选择最长的JSON匹配（通常是最完整的）
          cleanText = jsonMatches.reduce((longest: string, current: string) =>
            current.length > longest.length ? current : longest
          );
        }

        // 最终清理和格式化
        cleanText = cleanText.trim();

        console.log('增强清理后的JSON文本:', cleanText.substring(0, 500) + '...');
        jsonData = JSON.parse(cleanText);

        console.log('JSON解析成功，数据结构:', {
          hasFoods: !!jsonData.foods,
          foodsCount: jsonData.foods?.length || 0,
          hasMetadata: !!jsonData.analysisMetadata,
          hasAdvice: !!(jsonData.personalizedAdvice || jsonData.exerciseAdvice)
        });

      } catch (parseError) {
        console.error('增强JSON解析失败:', {
          originalText: text.substring(0, 200) + '...',
          parseError: parseError instanceof Error ? parseError.message : parseError
        });
        throw new Error('AI响应格式错误，无法解析结果。请重试或检查图片质量。');
      }

      // 验证数据格式
      if (!jsonData.foods || !Array.isArray(jsonData.foods)) {
        throw new Error('AI响应格式错误：foods字段缺失或格式错误');
      }

      // 如果没有识别到食物，返回空结果（这是正常情况，比如非食物图片）
      if (jsonData.foods.length === 0) {
        return {
          foods: [],
          rawResponse: response
        };
      }

      // 增强版食物数据验证和标准化
      const validatedFoods = jsonData.foods.map((food: any, index: number) => {
        // 基础字段验证
        if (!food.name || typeof food.name !== 'string') {
          throw new Error(`食物${index + 1}：名称缺失或格式错误`);
        }

        if (typeof food.calories !== 'number' || food.calories < 0) {
          throw new Error(`食物${index + 1}：卡路里数值错误 (${food.calories})`);
        }

        if (typeof food.weight !== 'number' || food.weight <= 0) {
          throw new Error(`食物${index + 1}：重量数值错误 (${food.weight})`);
        }

        if (typeof food.confidence !== 'number' || food.confidence < 0 || food.confidence > 1) {
          throw new Error(`食物${index + 1}：置信度数值错误 (${food.confidence})`);
        }

        // 营养成分验证和标准化
        const nutrition = food.nutrition || {};
        const standardizedNutrition = {
          protein: Math.max(0, Math.round((nutrition.protein || food.calories * 0.15 / 4) * 10) / 10),
          fat: Math.max(0, Math.round((nutrition.fat || food.calories * 0.25 / 9) * 10) / 10),
          carbs: Math.max(0, Math.round((nutrition.carbs || food.calories * 0.6 / 4) * 10) / 10),
          fiber: Math.max(0, Math.round((nutrition.fiber || food.calories * 0.05 / 4) * 10) / 10),
          sugar: Math.max(0, Math.round((nutrition.sugar || food.calories * 0.1 / 4) * 10) / 10),
          sodium: Math.max(0, Math.round(nutrition.sodium || food.calories * 0.5))
        };

        // 标签信息验证和标准化
        const labelInfo = food.labelInfo || {};
        const standardizedLabelInfo = {
          hasLabel: Boolean(labelInfo.hasLabel),
          servingSize: labelInfo.servingSize || null,
          labelAccuracy: typeof labelInfo.labelAccuracy === 'number' ?
            Math.round(labelInfo.labelAccuracy * 100) / 100 : null,
          brandName: labelInfo.brandName || null,
          productName: labelInfo.productName || null
        };

        // 分量分析信息（新增）
        const portionAnalysis = food.portionAnalysis || {};
        const standardizedPortionAnalysis = {
          estimatedPortion: typeof portionAnalysis.estimatedPortion === 'number' ?
            Math.round(portionAnalysis.estimatedPortion * 100) / 100 : 1.0,
          totalPackageWeight: portionAnalysis.totalPackageWeight || null,
          consumedWeight: Math.round(portionAnalysis.consumedWeight || food.weight)
        };

        return {
          name: food.name.trim(),
          calories: Math.round(food.calories),
          weight: Math.round(food.weight),
          confidence: Math.round(food.confidence * 100) / 100,
          alternatives: Array.isArray(food.alternatives) ? food.alternatives : [],
          dataSource: food.dataSource || (method === 'text' ? 'text_analysis' : 'visual_estimation'),
          nutrition: standardizedNutrition,
          labelInfo: standardizedLabelInfo,
          portionAnalysis: standardizedPortionAnalysis,
          // 保留图片索引信息（如果有）
          imageIndex: typeof food.imageIndex === 'number' ? food.imageIndex : 0,
          // 保留文本分析信息（如果有）
          textAnalysis: food.textAnalysis || null
        };
      });

      // 构建增强版返回结果
      const result: FoodRecognitionResult = {
        foods: validatedFoods,
        rawResponse: response,
        // 添加分析元数据（如果AI提供了）
        analysisMetadata: jsonData.analysisMetadata || {
          hasNutritionLabel: validatedFoods.some((food: any) => food.labelInfo.hasLabel),
          imageQuality: 'unknown' as const,
          recognitionMethod: method === 'text' ? 'text_analysis' as const : 'visual_estimation' as const,
          processingNotes: `成功识别${validatedFoods.length}种食物`
        },
        // 添加个性化建议（如果AI提供了）
        personalizedAdvice: jsonData.personalizedAdvice || null,
        exerciseAdvice: jsonData.exerciseAdvice || null,
        // 添加多图片分析信息（如果有）
        multiImageAnalysis: jsonData.multiImageAnalysis || null
      };

      console.log('食物识别结果处理完成:', {
        foodCount: result.foods.length,
        hasAdvice: !!(result.personalizedAdvice || result.exerciseAdvice),
        hasMetadata: !!result.analysisMetadata,
        totalCalories: result.foods.reduce((sum, food) => sum + food.calories, 0)
      });

      return result;
    } catch (error) {
      console.error('解析API响应失败:', error);
      throw new Error(error instanceof Error ? error.message : '解析AI响应失败');
    }
  }

  /**
   * 将文件转换为base64（移除data URL前缀，符合Gemini API要求）
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = () => {
        if (typeof reader.result === 'string') {
          // 移除data URL前缀 (data:image/...;base64,)，只保留纯净的base64数据
          const base64Data = reader.result.split(',')[1];
          if (base64Data) {
            resolve(base64Data);
          } else {
            reject(new Error('Base64数据提取失败'));
          }
        } else {
          reject(new Error('文件读取失败'));
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      reader.readAsDataURL(file);
    });
  }

  /**
   * 检查API配置
   */
  isConfigured(): boolean {
    return !!this.apiKey && !!this.endpoint;
  }

  /**
   * 获取配置信息
   */
  getConfig() {
    return {
      hasApiKey: !!this.apiKey,
      endpoint: this.endpoint,
      model: this.model,
      timeout: this.timeout
    };
  }
}

// 创建全局实例
export const geminiService = new GeminiService();