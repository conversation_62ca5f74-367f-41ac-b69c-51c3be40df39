import React, { useState } from 'react';
import { FoodRecord } from '@/shared/types';
import { formatDate } from '@/shared/utils';
import { WeightDisplay, CalorieDisplay, NumberDisplay } from '@/shared/components/atoms';

interface FoodNutritionModalProps {
  isOpen: boolean;
  onClose: () => void;
  foodRecord: FoodRecord | null;
  onSave: (updatedRecord: FoodRecord, newDate?: Date) => void;
  currentDate?: Date;
  initialEditMode?: boolean;
}

const FoodNutritionModal: React.FC<FoodNutritionModalProps> = ({
  isOpen,
  onClose,
  foodRecord,
  onSave,
  currentDate,
  initialEditMode = false
}) => {
  const [isEditing, setIsEditing] = useState(initialEditMode);
  const [editedRecord, setEditedRecord] = useState<FoodRecord | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate || new Date());

  // 初始化编辑状态
  React.useEffect(() => {
    if (foodRecord) {
      setEditedRecord({ ...foodRecord });
      // 设置默认日期为当前记录的日期或传入的当前日期
      const recordDate = new Date(foodRecord.recordedAt);
      setSelectedDate(currentDate || recordDate);
      // 设置初始编辑状态
      setIsEditing(initialEditMode);
    }
  }, [foodRecord, currentDate, initialEditMode]);

  if (!isOpen || !foodRecord || !editedRecord) return null;

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    if (editedRecord) {
      const updatedRecord = {
        ...editedRecord,
        isEdited: true,
        editedAt: new Date()
      };
      // 检查日期是否有变化
      const originalDate = new Date(foodRecord!.recordedAt);
      const hasDateChanged = selectedDate.toDateString() !== originalDate.toDateString();

      onSave(updatedRecord, hasDateChanged ? selectedDate : undefined);
      setIsEditing(false);
      onClose();
    }
  };

  const handleCancel = () => {
    setEditedRecord({ ...foodRecord });
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string | number) => {
    if (!editedRecord) return;

    if (field.startsWith('nutrition.')) {
      const nutritionField = field.split('.')[1];
      setEditedRecord({
        ...editedRecord,
        nutrition: {
          ...editedRecord.nutrition,
          [nutritionField]: typeof value === 'string' ? parseFloat(value) || 0 : value
        }
      });
    } else {
      setEditedRecord({
        ...editedRecord,
        [field]: typeof value === 'string' && (field === 'calories' || field === 'weight') 
          ? parseFloat(value) || 0 
          : value
      });
    }
  };

  const formatTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 0.8) return 'text-green-600';
    if (accuracy >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAccuracyText = (accuracy: number) => {
    if (accuracy >= 0.8) return '高准确度';
    if (accuracy >= 0.6) return '中等准确度';
    return '低准确度';
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4 pb-20"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(8px)'
      }}
      onClick={() => {
        if (!isEditing) {
          onClose();
        }
      }}
    >
      <div
        className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md max-h-[80vh] sm:max-h-[75vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 pb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">营养详情</h2>
            <p className="text-sm text-gray-500">查看和编辑食物营养信息</p>
          </div>
          <button
            onClick={onClose}
            className="btn btn-ghost btn-sm btn-circle hover:bg-gray-100 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容 */}
        <div className="flex-1 overflow-y-auto p-6 pt-2 space-y-6 pb-16">
          {/* 基础信息 */}
          <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-4 border border-blue-100 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              基础信息
            </h3>
            
            <div className="space-y-2">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">食物名称</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedRecord.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="input input-bordered input-sm w-full"
                  />
                ) : (
                  <div className="text-sm font-medium text-gray-900">{foodRecord.name}</div>
                )}
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">记录日期</label>
                {isEditing ? (
                  <input
                    type="date"
                    value={formatDate(selectedDate, 'yyyy-MM-dd')}
                    onChange={(e) => setSelectedDate(new Date(e.target.value))}
                    className="input input-bordered input-sm w-full"
                  />
                ) : (
                  <div className="text-sm font-medium text-gray-900">{formatDate(selectedDate, 'yyyy年MM月dd日')}</div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">重量 (g)</label>
                  {isEditing ? (
                    <input
                      type="number"
                      value={editedRecord.weight}
                      onChange={(e) => handleInputChange('weight', e.target.value)}
                      className="input input-bordered input-sm w-full"
                      min="0"
                      step="0.1"
                    />
                  ) : (
                    <div className="text-sm font-medium text-gray-900"><WeightDisplay value={foodRecord.weight} /></div>
                  )}
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">卡路里</label>
                  {isEditing ? (
                    <input
                      type="number"
                      value={editedRecord.calories}
                      onChange={(e) => handleInputChange('calories', e.target.value)}
                      className="input input-bordered input-sm w-full"
                      min="0"
                      step="1"
                    />
                  ) : (
                    <div className="text-sm font-medium text-gray-900"><CalorieDisplay value={foodRecord.calories} /></div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 营养成分 */}
          <div className="bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-xl p-4 border border-green-100 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              营养成分
            </h3>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">蛋白质 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.protein}
                    onChange={(e) => handleInputChange('nutrition.protein', e.target.value)}
                    className="input input-bordered input-sm w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-sm font-medium text-gray-900"><NumberDisplay value={foodRecord.nutrition.protein} unit="g" /></div>
                )}
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">脂肪 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.fat}
                    onChange={(e) => handleInputChange('nutrition.fat', e.target.value)}
                    className="input input-bordered input-sm w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-sm font-medium text-gray-900"><NumberDisplay value={foodRecord.nutrition.fat} unit="g" /></div>
                )}
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">碳水化合物 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.carbs}
                    onChange={(e) => handleInputChange('nutrition.carbs', e.target.value)}
                    className="input input-bordered input-sm w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-sm font-medium text-gray-900"><NumberDisplay value={foodRecord.nutrition.carbs} unit="g" /></div>
                )}
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">纤维 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.fiber}
                    onChange={(e) => handleInputChange('nutrition.fiber', e.target.value)}
                    className="input input-bordered input-sm w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-sm font-medium text-gray-900"><NumberDisplay value={foodRecord.nutrition.fiber} unit="g" /></div>
                )}
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">糖分 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.sugar}
                    onChange={(e) => handleInputChange('nutrition.sugar', e.target.value)}
                    className="input input-bordered input-sm w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-sm font-medium text-gray-900">{foodRecord.nutrition.sugar}g</div>
                )}
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">钠 (mg)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.sodium || 0}
                    onChange={(e) => handleInputChange('nutrition.sodium', e.target.value)}
                    className="input input-bordered input-sm w-full"
                    min="0"
                    step="1"
                  />
                ) : (
                  <div className="text-sm font-medium text-gray-900">{foodRecord.nutrition.sodium || 0}mg</div>
                )}
              </div>
            </div>
          </div>

          {/* 记录信息 */}
          <div className="bg-gradient-to-br from-gray-50 via-slate-50 to-zinc-50 rounded-xl p-4 border border-gray-100 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <div className="w-2 h-2 bg-gray-500 rounded-full mr-3"></div>
              记录信息
            </h3>

            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">添加时间：</span>
                <span className="font-medium">{formatTime(foodRecord.recordedAt)}</span>
              </div>

              {foodRecord.aiRecognition && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-600">识别方法：</span>
                    <span className="font-medium">
                      {foodRecord.aiRecognition.method === 'text' ? '文本识别' : '视觉识别'}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-600">准确度：</span>
                    <span className={`font-medium ${getAccuracyColor(foodRecord.aiRecognition.confidence)}`}>
                      {Math.round(foodRecord.aiRecognition.confidence * 100)}% ({getAccuracyText(foodRecord.aiRecognition.confidence)})
                    </span>
                  </div>
                </>
              )}

              {foodRecord.isEdited && (
                <div className="flex justify-between">
                  <span className="text-gray-600">最后编辑：</span>
                  <span className="font-medium">{formatTime(foodRecord.updatedAt)}</span>
                </div>
              )}

              {foodRecord.isEdited && (
                <div className="text-xs text-orange-600 bg-orange-50 rounded p-1 mt-1">
                  ⚠️ 此记录已被手动编辑
                </div>
              )}
            </div>
          </div>

          {/* 底部间距保障 */}
          <div className="h-4"></div>
        </div>

        {/* 底部按钮 */}
        <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
          {!isEditing ? (
            <>
              <button
                onClick={handleEdit}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                编辑
              </button>
              <button
                onClick={onClose}
                className="btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]"
              >
                关闭
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleSave}
                className="btn btn-success text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                保存
              </button>
              <button
                onClick={onClose}
                className="btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]"
              >
                关闭
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default FoodNutritionModal;
